import { Document, Types } from 'mongoose';

export interface IShop extends Document {
  _id: Types.ObjectId;
  userId: Types.ObjectId;
  shopName: string;
  shopImage: string;
  noOfProducts: number;
  createdAt: Date;
  updatedAt: Date;
}

export interface ICreateShopRequest {
  shopName: string;
  shopImage: string;
}

export interface IUpdateShopRequest {
  shopName?: string;
  shopImage?: string;
}
