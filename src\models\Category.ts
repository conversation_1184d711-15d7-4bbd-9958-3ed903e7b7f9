import mongoose, { Schem<PERSON> } from 'mongoose';
import { ICategory } from '../interfaces/category.interfaces';

const CategorySchema: Schema = new Schema({
  categoryImage: {
    type: String,
    required: [true, 'Category image is required'],
    trim: true,
    validate: {
      validator: function(v: string) {
        return /^https?:\/\/.+\.(jpg|jpeg|png|gif|webp)$/i.test(v);
      },
      message: 'Category image must be a valid image URL'
    }
  },
  categoryName: {
    type: String,
    required: [true, 'Category name is required'],
    trim: true,
    maxlength: [50, 'Category name cannot exceed 50 characters'],
    minlength: [2, 'Category name must be at least 2 characters']
  },
  categoryDescription: {
    type: String,
    required: [true, 'Category description is required'],
    trim: true,
    maxlength: [500, 'Category description cannot exceed 500 characters'],
    minlength: [10, 'Category description must be at least 10 characters']
  },
  categoryType: {
    type: String,
    required: [true, 'Category type is required'],
    trim: true,
    enum: {
      values: ['electronics', 'clothing', 'home', 'books', 'sports', 'beauty', 'automotive', 'toys', 'food', 'other'],
      message: 'Category type must be one of: electronics, clothing, home, books, sports, beauty, automotive, toys, food, other'
    }
  }
}, {
  timestamps: true
});

// Index for efficient queries
CategorySchema.index({ categoryName: 1 }, { unique: true });
CategorySchema.index({ categoryType: 1 });

export default mongoose.model<ICategory>('Category', CategorySchema);
