import { Router } from 'express';
import { 
  createProduct, 
  getAllProducts,
  getProductDetails,
  getProductStock,
  updateProduct,
  deleteProduct,
  markAsSold,
  getShopProducts,
  getShopDetailsWithProducts
} from '../controllers/product.controller';
import { verifyTokenForBusinessOwner } from '../middlewares/auth.middlewares';

const router = Router();

// Public routes - get products (no authentication required)
router.get('/', getAllProducts);
router.get('/:productId', getProductDetails);
router.get('/:productId/stock', getProductStock);

// Protected routes - require business owner authentication
router.use(verifyTokenForBusinessOwner);

// Create a new product
router.post('/', createProduct);

// Update product
router.put('/:productId', updateProduct);

// Delete product
router.delete('/:productId', deleteProduct);

// Mark product as sold
router.patch('/:productId/mark-sold', markAsSold);

// Get all products of a specific shop
router.get('/shop/:shopId', getShopProducts);

// Get shop details with product information
router.get('/shop/:shopId/details', getShopDetailsWithProducts);

export default router;
