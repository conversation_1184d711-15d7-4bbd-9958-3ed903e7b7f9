import { Request, Response } from 'express';
import { StatusCodes } from 'http-status-codes';
import { Types } from 'mongoose';
import Product from '../models/Product';
import Shop from '../models/Shop';
import Category from '../models/Category';
import SubCategory from '../models/SubCategory';
import { ICreateProductRequest, IUpdateProductRequest, IMarkAsSoldRequest } from '../interfaces/product.interfaces';
import { validateCreateProductData, validateUpdateProductData, validateMarkAsSoldData } from '../utils/validations';

// Create a new product
export const createProduct = async (req: Request, res: Response) => {
  try {
    const productData: ICreateProductRequest = req.body;
    const userId = req.user?._id;

    if (!userId) {
      return res.status(StatusCodes.UNAUTHORIZED).json({ 
        success: false, 
        message: 'User not authenticated' 
      });
    }

    // Validate input data
    const validation = validateCreateProductData(productData);
    if (!validation.isValid) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        success: false,
        message: 'Validation failed',
        errors: validation.errors
      });
    }

    // Verify shop ownership
    const shop = await Shop.findOne({ 
      _id: new Types.ObjectId(productData.shop), 
      userId: new Types.ObjectId(userId) 
    });

    if (!shop) {
      return res.status(StatusCodes.FORBIDDEN).json({
        success: false,
        message: 'Shop not found or you are not authorized to add products to this shop'
      });
    }

    // Verify category exists
    const category = await Category.findById(new Types.ObjectId(productData.productCategory));
    if (!category) {
      return res.status(StatusCodes.NOT_FOUND).json({
        success: false,
        message: 'Category not found'
      });
    }

    // Verify subcategory exists and belongs to the category
    const subCategory = await SubCategory.findOne({
      _id: new Types.ObjectId(productData.productSubCategory),
      category: new Types.ObjectId(productData.productCategory)
    });

    if (!subCategory) {
      return res.status(StatusCodes.NOT_FOUND).json({
        success: false,
        message: 'Subcategory not found or does not belong to the specified category'
      });
    }

    // Create new product
    const newProduct = new Product({
      productName: productData.productName.trim(),
      productDescription: productData.productDescription.trim(),
      productCategory: new Types.ObjectId(productData.productCategory),
      productSubCategory: new Types.ObjectId(productData.productSubCategory),
      shop: new Types.ObjectId(productData.shop),
      tags: productData.tags.map(tag => tag.trim()),
      productMedia: productData.productMedia,
      price: productData.price,
      sizes: productData.sizes,
      colors: productData.colors.map(color => color.trim()),
      stockQuantity: productData.stockQuantity,
      stockSold: 0,
      isActive: true
    });

    await newProduct.save();

    // Update shop's product count
    await Shop.findByIdAndUpdate(
      new Types.ObjectId(productData.shop),
      { $inc: { noOfProducts: 1 } }
    );

    // Populate references for response
    await newProduct.populate([
      { path: 'productCategory', select: 'categoryName categoryType' },
      { path: 'productSubCategory', select: 'subCategoryName' },
      { path: 'shop', select: 'shopName' }
    ]);

    return res.status(StatusCodes.CREATED).json({
      success: true,
      message: 'Product created successfully',
      product: {
        id: newProduct._id,
        productName: newProduct.productName,
        productDescription: newProduct.productDescription,
        productCategory: newProduct.productCategory,
        productSubCategory: newProduct.productSubCategory,
        shop: newProduct.shop,
        tags: newProduct.tags,
        isActive: newProduct.isActive,
        productMedia: newProduct.productMedia,
        price: newProduct.price,
        sizes: newProduct.sizes,
        colors: newProduct.colors,
        stockQuantity: newProduct.stockQuantity,
        stockSold: newProduct.stockSold,
        isLowStock: newProduct.isLowStock,
        createdAt: newProduct.createdAt
      }
    });

  } catch (error) {
    console.error('Error creating product:', error);
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: 'Error creating product',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};

// Get all products of a specific shop
export const getShopProducts = async (req: Request, res: Response) => {
  try {
    const { shopId } = req.params;
    const userId = req.user?._id;

    if (!userId) {
      return res.status(StatusCodes.UNAUTHORIZED).json({ 
        success: false, 
        message: 'User not authenticated' 
      });
    }

    if (!Types.ObjectId.isValid(shopId)) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        success: false,
        message: 'Invalid shop ID'
      });
    }

    // Verify shop ownership
    const shop = await Shop.findOne({ 
      _id: new Types.ObjectId(shopId), 
      userId: new Types.ObjectId(userId) 
    });

    if (!shop) {
      return res.status(StatusCodes.FORBIDDEN).json({
        success: false,
        message: 'Shop not found or you are not authorized to view products of this shop'
      });
    }

    const products = await Product.find({ shop: new Types.ObjectId(shopId) })
      .populate('productCategory', 'categoryName categoryType')
      .populate('productSubCategory', 'subCategoryName')
      .populate('shop', 'shopName')
      .sort({ createdAt: -1 })
      .select('-__v');

    return res.status(StatusCodes.OK).json({
      success: true,
      message: 'Shop products retrieved successfully',
      shop: {
        id: shop._id,
        shopName: shop.shopName,
        noOfProducts: shop.noOfProducts
      },
      count: products.length,
      products: products.map(product => ({
        id: product._id,
        productName: product.productName,
        productDescription: product.productDescription,
        productCategory: product.productCategory,
        productSubCategory: product.productSubCategory,
        shop: product.shop,
        tags: product.tags,
        isActive: product.isActive,
        productMedia: product.productMedia,
        price: product.price,
        sizes: product.sizes,
        colors: product.colors,
        stockQuantity: product.stockQuantity,
        stockSold: product.stockSold,
        isLowStock: product.isLowStock,
        createdAt: product.createdAt,
        updatedAt: product.updatedAt
      }))
    });

  } catch (error) {
    console.error('Error getting shop products:', error);
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: 'Error retrieving shop products',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};

// Get shop details with product count
export const getShopDetailsWithProducts = async (req: Request, res: Response) => {
  try {
    const { shopId } = req.params;
    const userId = req.user?._id;

    if (!userId) {
      return res.status(StatusCodes.UNAUTHORIZED).json({ 
        success: false, 
        message: 'User not authenticated' 
      });
    }

    if (!Types.ObjectId.isValid(shopId)) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        success: false,
        message: 'Invalid shop ID'
      });
    }

    // Verify shop ownership
    const shop = await Shop.findOne({ 
      _id: new Types.ObjectId(shopId), 
      userId: new Types.ObjectId(userId) 
    });

    if (!shop) {
      return res.status(StatusCodes.FORBIDDEN).json({
        success: false,
        message: 'Shop not found or you are not authorized to view this shop'
      });
    }

    // Get product count and low stock count
    const totalProducts = await Product.countDocuments({ shop: new Types.ObjectId(shopId) });
    const activeProducts = await Product.countDocuments({ shop: new Types.ObjectId(shopId), isActive: true });
    const lowStockProducts = await Product.countDocuments({ shop: new Types.ObjectId(shopId), isLowStock: true });

    return res.status(StatusCodes.OK).json({
      success: true,
      message: 'Shop details retrieved successfully',
      shop: {
        id: shop._id,
        shopName: shop.shopName,
        shopImage: shop.shopImage,
        noOfProducts: shop.noOfProducts,
        totalProducts,
        activeProducts,
        lowStockProducts,
        createdAt: shop.createdAt,
        updatedAt: shop.updatedAt
      }
    });

  } catch (error) {
    console.error('Error getting shop details:', error);
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: 'Error retrieving shop details',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};

// Get specific product details
export const getProductDetails = async (req: Request, res: Response) => {
  try {
    const { productId } = req.params;

    if (!Types.ObjectId.isValid(productId)) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        success: false,
        message: 'Invalid product ID'
      });
    }

    const product = await Product.findById(new Types.ObjectId(productId))
      .populate('productCategory', 'categoryName categoryType categoryImage')
      .populate('productSubCategory', 'subCategoryName')
      .populate('shop', 'shopName shopImage')
      .select('-__v');

    if (!product) {
      return res.status(StatusCodes.NOT_FOUND).json({
        success: false,
        message: 'Product not found'
      });
    }

    return res.status(StatusCodes.OK).json({
      success: true,
      message: 'Product details retrieved successfully',
      product: {
        id: product._id,
        productName: product.productName,
        productDescription: product.productDescription,
        productCategory: product.productCategory,
        productSubCategory: product.productSubCategory,
        shop: product.shop,
        tags: product.tags,
        isActive: product.isActive,
        productMedia: product.productMedia,
        price: product.price,
        sizes: product.sizes,
        colors: product.colors,
        stockQuantity: product.stockQuantity,
        stockSold: product.stockSold,
        isLowStock: product.isLowStock,
        createdAt: product.createdAt,
        updatedAt: product.updatedAt
      }
    });

  } catch (error) {
    console.error('Error getting product details:', error);
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: 'Error retrieving product details',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};

// Get remaining stock quantity of a specific product
export const getProductStock = async (req: Request, res: Response) => {
  try {
    const { productId } = req.params;

    if (!Types.ObjectId.isValid(productId)) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        success: false,
        message: 'Invalid product ID'
      });
    }

    const product = await Product.findById(new Types.ObjectId(productId))
      .select('productName stockQuantity stockSold isLowStock');

    if (!product) {
      return res.status(StatusCodes.NOT_FOUND).json({
        success: false,
        message: 'Product not found'
      });
    }

    return res.status(StatusCodes.OK).json({
      success: true,
      message: 'Product stock information retrieved successfully',
      stock: {
        productId: product._id,
        productName: product.productName,
        stockQuantity: product.stockQuantity,
        stockSold: product.stockSold,
        remainingStock: product.stockQuantity,
        isLowStock: product.isLowStock
      }
    });

  } catch (error) {
    console.error('Error getting product stock:', error);
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: 'Error retrieving product stock',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};

// Mark product as sold (deduct stock)
export const markAsSold = async (req: Request, res: Response) => {
  try {
    const { productId } = req.params;
    const { quantity }: IMarkAsSoldRequest = req.body;
    const userId = req.user?._id;

    if (!userId) {
      return res.status(StatusCodes.UNAUTHORIZED).json({
        success: false,
        message: 'User not authenticated'
      });
    }

    if (!Types.ObjectId.isValid(productId)) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        success: false,
        message: 'Invalid product ID'
      });
    }

    // Validate input data
    const validation = validateMarkAsSoldData({ quantity });
    if (!validation.isValid) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        success: false,
        message: 'Validation failed',
        errors: validation.errors
      });
    }

    // Find product and verify ownership through shop
    const product = await Product.findById(new Types.ObjectId(productId))
      .populate('shop', 'userId');

    if (!product) {
      return res.status(StatusCodes.NOT_FOUND).json({
        success: false,
        message: 'Product not found'
      });
    }

    // Check if user owns the shop
    const shop = product.shop as any;
    if (shop.userId.toString() !== userId) {
      return res.status(StatusCodes.FORBIDDEN).json({
        success: false,
        message: 'You are not authorized to update this product'
      });
    }

    // Check if enough stock is available
    if (product.stockQuantity < quantity) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        success: false,
        message: `Insufficient stock. Available: ${product.stockQuantity}, Requested: ${quantity}`
      });
    }

    // Update stock quantities
    product.stockQuantity -= quantity;
    product.stockSold += quantity;
    product.isLowStock = product.stockQuantity < 50;

    await product.save();

    return res.status(StatusCodes.OK).json({
      success: true,
      message: 'Product marked as sold successfully',
      product: {
        id: product._id,
        productName: product.productName,
        stockQuantity: product.stockQuantity,
        stockSold: product.stockSold,
        isLowStock: product.isLowStock,
        quantitySold: quantity
      }
    });

  } catch (error) {
    console.error('Error marking product as sold:', error);
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: 'Error marking product as sold',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};

// Update product
export const updateProduct = async (req: Request, res: Response) => {
  try {
    const { productId } = req.params;
    const updateData: IUpdateProductRequest = req.body;
    const userId = req.user?._id;

    if (!userId) {
      return res.status(StatusCodes.UNAUTHORIZED).json({
        success: false,
        message: 'User not authenticated'
      });
    }

    if (!Types.ObjectId.isValid(productId)) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        success: false,
        message: 'Invalid product ID'
      });
    }

    // Validate input data
    const validation = validateUpdateProductData(updateData);
    if (!validation.isValid) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        success: false,
        message: 'Validation failed',
        errors: validation.errors
      });
    }

    // Find product and verify ownership through shop
    const product = await Product.findById(new Types.ObjectId(productId))
      .populate('shop', 'userId');

    if (!product) {
      return res.status(StatusCodes.NOT_FOUND).json({
        success: false,
        message: 'Product not found'
      });
    }

    // Check if user owns the shop
    const shop = product.shop as any;
    if (shop.userId.toString() !== userId) {
      return res.status(StatusCodes.FORBIDDEN).json({
        success: false,
        message: 'You are not authorized to update this product'
      });
    }

    // If category is being updated, verify it exists
    if (updateData.productCategory) {
      const category = await Category.findById(new Types.ObjectId(updateData.productCategory));
      if (!category) {
        return res.status(StatusCodes.NOT_FOUND).json({
          success: false,
          message: 'Category not found'
        });
      }
    }

    // If subcategory is being updated, verify it exists and belongs to the category
    if (updateData.productSubCategory) {
      const targetCategoryId = updateData.productCategory ?
        new Types.ObjectId(updateData.productCategory) :
        product.productCategory;

      const subCategory = await SubCategory.findOne({
        _id: new Types.ObjectId(updateData.productSubCategory),
        category: targetCategoryId
      });

      if (!subCategory) {
        return res.status(StatusCodes.NOT_FOUND).json({
          success: false,
          message: 'Subcategory not found or does not belong to the specified category'
        });
      }
    }

    // Prepare update object
    const updateObject: any = {};
    if (updateData.productName) updateObject.productName = updateData.productName.trim();
    if (updateData.productDescription) updateObject.productDescription = updateData.productDescription.trim();
    if (updateData.productCategory) updateObject.productCategory = new Types.ObjectId(updateData.productCategory);
    if (updateData.productSubCategory) updateObject.productSubCategory = new Types.ObjectId(updateData.productSubCategory);
    if (updateData.tags) updateObject.tags = updateData.tags.map(tag => tag.trim());
    if (updateData.isActive !== undefined) updateObject.isActive = updateData.isActive;
    if (updateData.productMedia) updateObject.productMedia = updateData.productMedia;
    if (updateData.price !== undefined) updateObject.price = updateData.price;
    if (updateData.sizes) updateObject.sizes = updateData.sizes;
    if (updateData.colors) updateObject.colors = updateData.colors.map(color => color.trim());
    if (updateData.stockQuantity !== undefined) {
      updateObject.stockQuantity = updateData.stockQuantity;
      updateObject.isLowStock = updateData.stockQuantity < 50;
    }

    // Update product
    const updatedProduct = await Product.findByIdAndUpdate(
      new Types.ObjectId(productId),
      updateObject,
      { new: true, runValidators: true }
    ).populate([
      { path: 'productCategory', select: 'categoryName categoryType' },
      { path: 'productSubCategory', select: 'subCategoryName' },
      { path: 'shop', select: 'shopName' }
    ]);

    return res.status(StatusCodes.OK).json({
      success: true,
      message: 'Product updated successfully',
      product: {
        id: updatedProduct!._id,
        productName: updatedProduct!.productName,
        productDescription: updatedProduct!.productDescription,
        productCategory: updatedProduct!.productCategory,
        productSubCategory: updatedProduct!.productSubCategory,
        shop: updatedProduct!.shop,
        tags: updatedProduct!.tags,
        isActive: updatedProduct!.isActive,
        productMedia: updatedProduct!.productMedia,
        price: updatedProduct!.price,
        sizes: updatedProduct!.sizes,
        colors: updatedProduct!.colors,
        stockQuantity: updatedProduct!.stockQuantity,
        stockSold: updatedProduct!.stockSold,
        isLowStock: updatedProduct!.isLowStock,
        updatedAt: updatedProduct!.updatedAt
      }
    });

  } catch (error) {
    console.error('Error updating product:', error);
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: 'Error updating product',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};

// Delete product
export const deleteProduct = async (req: Request, res: Response) => {
  try {
    const { productId } = req.params;
    const userId = req.user?._id;

    if (!userId) {
      return res.status(StatusCodes.UNAUTHORIZED).json({
        success: false,
        message: 'User not authenticated'
      });
    }

    if (!Types.ObjectId.isValid(productId)) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        success: false,
        message: 'Invalid product ID'
      });
    }

    // Find product and verify ownership through shop
    const product = await Product.findById(new Types.ObjectId(productId))
      .populate('shop', 'userId shopName');

    if (!product) {
      return res.status(StatusCodes.NOT_FOUND).json({
        success: false,
        message: 'Product not found'
      });
    }

    // Check if user owns the shop
    const shop = product.shop as any;
    if (shop.userId.toString() !== userId) {
      return res.status(StatusCodes.FORBIDDEN).json({
        success: false,
        message: 'You are not authorized to delete this product'
      });
    }

    // Delete product
    await Product.findByIdAndDelete(new Types.ObjectId(productId));

    // Update shop's product count
    await Shop.findByIdAndUpdate(
      product.shop,
      { $inc: { noOfProducts: -1 } }
    );

    return res.status(StatusCodes.OK).json({
      success: true,
      message: 'Product deleted successfully',
      deletedProduct: {
        id: product._id,
        productName: product.productName,
        shop: shop.shopName
      }
    });

  } catch (error) {
    console.error('Error deleting product:', error);
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: 'Error deleting product',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};

// Get all products (with filtering options)
export const getAllProducts = async (req: Request, res: Response) => {
  try {
    const {
      categoryId,
      subCategoryId,
      shopId,
      isActive,
      isLowStock,
      minPrice,
      maxPrice,
      search,
      page = 1,
      limit = 10
    } = req.query;

    // Build filter object
    const filter: any = {};

    if (categoryId && Types.ObjectId.isValid(categoryId as string)) {
      filter.productCategory = new Types.ObjectId(categoryId as string);
    }

    if (subCategoryId && Types.ObjectId.isValid(subCategoryId as string)) {
      filter.productSubCategory = new Types.ObjectId(subCategoryId as string);
    }

    if (shopId && Types.ObjectId.isValid(shopId as string)) {
      filter.shop = new Types.ObjectId(shopId as string);
    }

    if (isActive !== undefined) {
      filter.isActive = isActive === 'true';
    }

    if (isLowStock !== undefined) {
      filter.isLowStock = isLowStock === 'true';
    }

    if (minPrice || maxPrice) {
      filter.price = {};
      if (minPrice) filter.price.$gte = parseFloat(minPrice as string);
      if (maxPrice) filter.price.$lte = parseFloat(maxPrice as string);
    }

    if (search) {
      filter.$text = { $search: search as string };
    }

    const pageNum = parseInt(page as string) || 1;
    const limitNum = parseInt(limit as string) || 10;
    const skip = (pageNum - 1) * limitNum;

    const products = await Product.find(filter)
      .populate('productCategory', 'categoryName categoryType')
      .populate('productSubCategory', 'subCategoryName')
      .populate('shop', 'shopName shopImage')
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limitNum)
      .select('-__v');

    const totalProducts = await Product.countDocuments(filter);
    const totalPages = Math.ceil(totalProducts / limitNum);

    return res.status(StatusCodes.OK).json({
      success: true,
      message: 'Products retrieved successfully',
      pagination: {
        currentPage: pageNum,
        totalPages,
        totalProducts,
        hasNextPage: pageNum < totalPages,
        hasPrevPage: pageNum > 1
      },
      products: products.map(product => ({
        id: product._id,
        productName: product.productName,
        productDescription: product.productDescription,
        productCategory: product.productCategory,
        productSubCategory: product.productSubCategory,
        shop: product.shop,
        tags: product.tags,
        isActive: product.isActive,
        productMedia: product.productMedia,
        price: product.price,
        sizes: product.sizes,
        colors: product.colors,
        stockQuantity: product.stockQuantity,
        stockSold: product.stockSold,
        isLowStock: product.isLowStock,
        createdAt: product.createdAt,
        updatedAt: product.updatedAt
      }))
    });

  } catch (error) {
    console.error('Error getting products:', error);
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: 'Error retrieving products',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};
