import { Request, Response, NextFunction } from "express";
import Jwt from "jsonwebtoken";
import { StatusCodes } from "http-status-codes";
import User from "../models/User";
import { UserPayload } from "../types/express";
import dotenv from "dotenv";
import { Types } from 'mongoose';
dotenv.config();

export const verifyToken = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  const authHeader = req.headers.authorization;
  if (authHeader) {
    const token = authHeader.split(" ")[1];
    if (!token) {
      res
        .status(StatusCodes.UNAUTHORIZED)
        .json({ message: "Access Denied Invalid Token" });
      return;
    }
    Jwt.verify(
      token,
      process.env.JWT_SECRET || "",
      async (err: any, decoded: any) => {
        if (err) {
          console.log('JWT verification error:', err);
          res
            .status(StatusCodes.UNAUTHORIZED)
            .json({ message: "Token not matched" });
          return;
        }
        // const checkUser = await User.findById(decoded?._id);
        // const checkUser = await User.findById(decoded?._id);
        // if (!checkUser) {
        //   res
        //     .status(StatusCodes.UNAUTHORIZED)
        //     .json({ message: "User not found" });
        //   return;
        // }
        // if (!checkUser?.isVerified && checkUser?.role !== "admin") {
        //   res.status(StatusCodes.UNAUTHORIZED).json({
        //     message: `${
        //       checkUser?.role === "child"
        //         ? "Account Not Verified"
        //         : checkUser?.role === "parent"
        //         ? "Business Account not Approved from Admin yet"
        //         : "Account Not Verified"
        //     } `,
        //   });
        //   return;
        // }
        const user = decoded as UserPayload;
        req.user = user;
        req.userIdFromToken = user?._id;
        next();
      }
    );
  } else {
    res
      .status(StatusCodes.UNAUTHORIZED)
      .json({ message: "Access Denied Invalid Token" });
    return;
  }
};

export const verifyTokenForAdmin = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  verifyToken(req, res, async () => {
    const user = req.user as UserPayload;
    const checkUser = await User.findById(new Types.ObjectId(user?._id));
    if (!checkUser) {
      res
        .status(StatusCodes.UNAUTHORIZED)
        .json({ message: "Admin not found" });
      return;
    }
    if (user?.role !== "admin") {
      return res.status(StatusCodes.UNAUTHORIZED).json({
        message: "You are not authorized to access this resource",
      });
    }
    next();
  });
};

export const verifyTokenForBusinessOwner = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  verifyToken(req, res, async () => {
    const user = req.user as UserPayload;
    console.log('User in auth middelware = ', user);
    const userIdFromToken = user?._id;
    console.log('User ID from token in auth middelware = ', userIdFromToken);
    const checkUser = await User.findById(new Types.ObjectId(user?._id));
    //console.log('Check User in auth middelware = ', checkUser);
    if (!checkUser) {
        res
          .status(StatusCodes.UNAUTHORIZED)
          .json({ message: "Parent not found" });
        return;
    }
    
    if(!checkUser?.isEmailVerified){
        res.status(StatusCodes.UNAUTHORIZED).json({
          message: "Account Not Verified",
        });
        return;
    }
    if (user?.role !== "businessOwner") {
      return res.status(StatusCodes.UNAUTHORIZED).json({
        message: "You are not authorized to access this resource",
      });
    }
    next();
  });
};

export const verifyTokenForUser = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  verifyToken(req, res, async () => {
    const user = req.user as UserPayload;
    const userIdFromToken = user?._id;
    console.log('User ID from token in auth middelware = ', userIdFromToken);
    const checkUser = await User.findById(new Types.ObjectId(user?._id));
    if (!checkUser) {
        res
          .status(StatusCodes.UNAUTHORIZED)
          .json({ message: "Child not found" });
        return;
    }
    if(!checkUser?.isEmailVerified){
        res.status(StatusCodes.UNAUTHORIZED).json({
          message: "Account Not Verified",
        });
        return;
    }
    if (user?.role !== "user") {
      return res.status(StatusCodes.UNAUTHORIZED).json({
        message: "You are not authorized to access this resource",
      });
    }
    next();
  });
};
