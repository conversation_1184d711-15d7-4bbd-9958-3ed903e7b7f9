import dotenv from 'dotenv';
import express from 'express';
import { createServer } from 'http';
import connectDB from './config/db';
import cors from "cors";
import bodyParser from "body-parser";
import helmet from "helmet";
import { rateLimiter } from "./utils/rateLimit";
import { StatusCodes } from "http-status-codes";
import authRoutes from './routes/auth.routes';
import shopRoutes from './routes/shop.routes';
import categoryRoutes from './routes/category.routes';
import subcategoryRoutes from './routes/subcategory.routes';
import productRoutes from './routes/product.routes';
import adminRoutes from './routes/admin.routes';

// ----------- Config -------------------
dotenv.config();

// ----------- Server -------------------
const app = express();
const server = createServer(app);
const port = process.env.PORT || 5001;

// ---------- Middlewares ----------------------------
app.use(bodyParser.json({ limit: "50mb" }));
app.use(bodyParser.urlencoded({ limit: "50mb", extended: true }));
app.use(cors());
app.use(helmet());
app.use(rateLimiter());


// ---------- Routes ----------------------------
app.use('/api/auth', authRoutes);
app.use('/api/shops', shopRoutes);
app.use('/api/categories', categoryRoutes);
app.use('/api/subcategories', subcategoryRoutes);
app.use('/api/products', productRoutes);
app.use('/api/admin', adminRoutes);




// Connect to MongoDB
connectDB();


// Start server
server.listen(port, () => {
  console.log('Server is running on port 5001');
});
