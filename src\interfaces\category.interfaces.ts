import { Document, Types } from 'mongoose';

export interface ICategory extends Document {
  _id: Types.ObjectId;
  categoryImage: string;
  categoryName: string;
  categoryDescription: string;
  categoryType: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface ICreateCategoryRequest {
  categoryImage: string;
  categoryName: string;
  categoryDescription: string;
  categoryType: string;
}

export interface IUpdateCategoryRequest {
  categoryImage?: string;
  categoryName?: string;
  categoryDescription?: string;
  categoryType?: string;
}
